import { Component, OnInit } from '@angular/core';
import { NavigationEnd,ActivatedRoute, Router, RouterOutlet } from '@angular/router';
import { LoadingComponent } from './components/loading/loading.component';
import { AsyncPipe, CommonModule } from '@angular/common';
import { AuthService } from '@auth0/auth0-angular';
import { ThemeGeneratorService } from 'cax-design-system/api';
import { NavigationModule } from 'cax-design-system/navigation';
import { filter } from 'rxjs';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, LoadingComponent, CommonModule, AsyncPipe, NavigationModule],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss',
  providers: [ThemeGeneratorService],
})
export class AppComponent implements OnInit {
  navBarExpanded: boolean = false;
  topNavList: any[] = [];
  bottomNavList: any[] = [];
  activeTab: any = { position: 'top', index: 0 };

  themeCode = '#5946b9';

  constructor(
    public router: Router,
    private activatedRoute: ActivatedRoute,
    public auth: AuthService,
    private themeGenerator: ThemeGeneratorService
  ) {

    this.themeGenerator.applyTheme(this.themeCode);
  }

  ngOnInit(): void {
    this.initialiseNavList();
    this.router.events
        .pipe(
            filter(
                (event): event is NavigationEnd =>
                    event instanceof NavigationEnd
            )
        )
        .subscribe((event: NavigationEnd) => console.log(event));
}

initialiseNavList() {
    this.topNavList = [
        {
            label: 'Batches',
            icon: 'cax-document-text',
            command: () => {
                this.router.navigate(['home']);
            },
        },
        {
            label: 'Tags',
            icon: 'cax-tag-icon',
            command: () => {
                this.router.navigate(['/#']);
            },
        },
    ];
    this.bottomNavList = [
        {
            label: 'Apps',
            icon: 'cax-widget-2',
            command: () => {
                console.log('Redirect to apps');
            },
        },
    ];
}

navBarStatus(event: any) {
    this.navBarExpanded = event;
}

changeActiveItem(event: any) {
    this.activeTab = event.activeTab;
}

openHelpCentre() {
    this.router.navigate(['/#']);
}
}
