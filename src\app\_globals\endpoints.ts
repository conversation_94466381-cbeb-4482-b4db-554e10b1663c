const ENDPOINTS: any = {
  user: {
    me: {
      pathA: '/api/subscriptions/',
      pathB: '/me',
    },
    permissions: {
      pathA: '/api/subscriptions/',
      pathB: '/permissions',
    },
  },

  home: {
    batchList: 'https://m2.app-dev.datax.ai/api/subscriptions/',
    singleBatchList: 'https://m2.app-dev.datax.ai/api/subscriptions/',
    getTemplateId: {
      pathA: '/api/subscriptions/',
      pathB: '/file_formats',
    },
    inputFileDownload: {
      pathA: '/api/subscriptions/',
      pathB: '/batches/',
      pathC: '/download_input'
    },
    stats: 'https://m2.app-dev.datax.ai/api/subscriptions/',
    labelList: 'https://m2.app-dev.datax.ai/api/subscriptions/',
    statusUpdate: 'https://m2.app-dev.datax.ai/api/subscriptions/',
    deleteBatch: '/api/subscriptions/',
    count: '/api/subscriptions/',
    generateOutput: '/api/subscriptions/',
    downloadOutputFile: '/api/subscriptions/',
    updateETA: '/api/subscriptions/',
    batchLog: {
      pathA: '/api/subscriptions/',
      pathB: '/log',
    },
    batchStatus: {
      pathA: '/api/subscriptions/',
      pathB: '/batches/',
      pathC: '/input_row_status',
    },
  },
  file_upload: {
    get_signed_url: '/api/subscriptions/',
    upload_complete: '/api/subscriptions/',
  },
  review: {
    categoryFilter: '/api/subscriptions/',
    rowIndex: '/api/subscriptions/',
    acceptAllSuggestion: '/api/subscriptions/',
    reviewProductDetail: {
      pathA: '/api/subscriptions/',
      pathB: '/review/list',
    },
    reviewModeProductDetail: '/api/subscriptions/',
  },
  product_details: {
    prediction_list: '/api/subscriptions/',
    attributeList: '/api/subscriptions/',
    editPredictions: '/api/subscriptions/',
  },

  undo: {
    undoCall: '/api/subscriptions/',
  },

  products: {
    productList: {
      pathA: '/api/subscriptions/',
      pathB: '/inputs',
    },
    productHeaders: {
      pathA: '/api/subscriptions/',
      pathB: '/headers',
    },
    bucketCount: {
      pathA: '/api/subscriptions/',
      pathB: '/inputs/row_count',
    },
    batchProgress: {
      pathA: '/api/subscriptions/',
      pathB: '/batch_progress',
    },
    bucketUpdate: {
      pathA: '/api/subscriptions/',
      pathB: '/inputs/',
      pathC: '/bucket_update',
    },
  },
  comments: {
    commentsList: {
      pathA: '/api/subscriptions/',
      pathB: '/comments',
    },
    postComment: {
      pathA: '/api/subscriptions/',
      pathB: '/comments/create',
    },
    userNamesToTag: {
      pathA: '/api/subscriptions/',
      pathB: '/usernames',
    },
    resolve: {
      pathA: '/api/subscriptions/',
      pathB: '/comments/resolve',
    },
    edit: {
      pathA: '/api/subscriptions/',
      pathB: '/comments/',
      pathC: '/edit',
    },
    delete: {
      pathA: '/api/subscriptions/',
      pathB: '/comments/',
      pathC: '/delete',
    },
    upload: {
      pathA: '/api/subscriptions/',
      pathB: '/attachments/embedded_file/upload',
    },
  },
  settings: {
    apiUsage: {
      pathA: '/api/subscriptions/',
      pathB: '/api_usage',
    },
  },
  help: {
    feedback: {
      pathA: '/api/subscriptions/',
      pathB: '/feedback',
    },
  },
};
export { ENDPOINTS };
