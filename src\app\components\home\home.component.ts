import { Component, OnInit } from '@angular/core';
import { HomeService } from '../../../services/home.service';
import { CommonModule } from '@angular/common';
import { Globals } from '../../_globals/endpoints.global';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-home',
  imports: [CommonModule],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit {
  subscriptionId: string | null = null;
  batchList: any[] = [];
  stats: any = null;
  labelList: any[] = [];
  loading: boolean = false;
  error: string | null = null;

  constructor(
    private homeService: HomeService,
    private globals: Globals,
    private http: HttpClient
  ) {}

  ngOnInit(): void {
    this.loadSubscriptionId();
    this.debugEnvironment();

    if (this.subscriptionId) {
      this.loadHomeData();
    } else {
      this.error = 'No subscription ID found. Please ensure you came from the dashboard with a valid subscription ID.';
      console.error('❌ No subscription ID found in localStorage');
      console.log('🔍 Current localStorage contents:', {
        SubscriptionID: localStorage.getItem('SubscriptionID'),
        user: localStorage.getItem('user'),
        allKeys: Object.keys(localStorage)
      });
    }
  }

  private debugEnvironment(): void {
    console.log('🔧 Environment Debug Information:');
    console.log('🌐 Current URL:', window.location.href);
    console.log('🔑 Subscription ID:', this.subscriptionId);
    console.log('📦 LocalStorage SubscriptionID:', localStorage.getItem('SubscriptionID'));
    console.log('👤 User in localStorage:', localStorage.getItem('user'));
    console.log('🔗 All localStorage keys:', Object.keys(localStorage));

    // Test URL construction
    this.debugUrlConstruction();

    // Test proxy configuration
    this.testProxyConfiguration();
  }

  private debugUrlConstruction(): void {
    console.log('🔧 URL Construction Debug:');

    // Test the globals service URL construction
    const batchListBase = this.globals.urlJoin('home', 'batchList');
    console.log('📍 Batch list base URL:', batchListBase);

    if (this.subscriptionId) {
      const fullBatchUrl = batchListBase + this.subscriptionId + '/batches/list';
      console.log('📍 Full batch list URL:', fullBatchUrl);

      const statsBase = this.globals.urlJoin('home', 'stats');
      const fullStatsUrl = statsBase + this.subscriptionId + '/stats';
      console.log('📍 Full stats URL:', fullStatsUrl);

      const labelsBase = this.globals.urlJoin('home', 'labelList');
      const fullLabelsUrl = labelsBase + this.subscriptionId + '/label_list';
      console.log('📍 Full labels URL:', fullLabelsUrl);
    } else {
      console.log('❌ Cannot construct full URLs without subscription ID');
    }
  }

  private testProxyConfiguration(): void {
    console.log('🔧 Testing Proxy Configuration:');

    // Test if proxy is working by making a simple API call
    const testUrl = '/api/test';
    console.log('🧪 Testing proxy with URL:', testUrl);

    this.http.get(testUrl).subscribe({
      next: (response) => {
        console.log('✅ Proxy test successful:', response);
      },
      error: (error) => {
        console.log('❌ Proxy test failed:', {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          message: error.message
        });

        // If 404, proxy might be working but endpoint doesn't exist
        if (error.status === 404) {
          console.log('ℹ️ 404 error suggests proxy is working but endpoint /api/test does not exist');
          console.log('🔍 Let\'s test if the backend server is accessible...');
          this.testBackendDirectly();
        }
        // If connection refused or network error, proxy might not be working
        else if (error.status === 0) {
          console.log('❌ Network error suggests proxy configuration issue');
        }
      }
    });
  }

  private testBackendDirectly(): void {
    // Test direct access to backend server
    const directUrl = 'https://m2.app-dev.datax.ai/api/subscriptions/' + this.subscriptionId + '/stats';
    console.log('🌐 Testing direct backend access:', directUrl);

    this.http.get(directUrl).subscribe({
      next: (response) => {
        console.log('✅ Direct backend access successful:', response);
        console.log('🔧 This means the backend is working, but proxy is not routing correctly');
      },
      error: (error) => {
        console.log('❌ Direct backend access failed:', {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          message: error.message
        });

        if (error.status === 0) {
          console.log('❌ CORS or network issue - backend server might be down or not accessible');
        } else if (error.status === 401 || error.status === 403) {
          console.log('🔐 Authentication issue - this is expected for direct access');
        }
      }
    });
  }

  private loadSubscriptionId(): void {
    // Get subscription ID from localStorage
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    console.log('Subscription ID from localStorage:', this.subscriptionId);
  }

  private loadHomeData(): void {
    if (!this.subscriptionId) {
      this.error = 'Subscription ID is required for API calls';
      return;
    }

    this.loading = true;
    this.error = null;

    // Load batch list
    this.loadBatchList();

    // Load stats
    this.loadStats();

    // Load labels
    this.loadLabels();
  }

  private loadBatchList(): void {
    console.log('🔍 Loading batch list with subscription ID:', this.subscriptionId);

    this.homeService.getBatchList(
      '1',      // page
      '10',     // size
      '',       // status
      '',       // search
      '',       // start_date
      '',       // end_date
      this.subscriptionId!,
      []        // tags
    ).subscribe({
      next: (response) => {
        this.batchList = response.result || response;
        console.log('✅ Batch list loaded successfully:', this.batchList);
      },
      error: (error) => {
        console.error('❌ Error loading batch list:', error);
        console.error('❌ Error details:', {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          message: error.message
        });
        this.error = `Failed to load batch list: ${error.status} ${error.statusText}. URL: ${error.url}`;
      }
    });
  }

  private loadStats(): void {
    console.log('🔍 Loading stats with subscription ID:', this.subscriptionId);

    this.homeService.getStats(this.subscriptionId!).subscribe({
      next: (response) => {
        this.stats = response.result || response;
        console.log('✅ Stats loaded successfully:', this.stats);
      },
      error: (error) => {
        console.error('❌ Error loading stats:', error);
        console.error('❌ Error details:', {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          message: error.message
        });
        this.error = `Failed to load stats: ${error.status} ${error.statusText}. URL: ${error.url}`;
      }
    });
  }

  private loadLabels(): void {
    console.log('🔍 Loading labels with subscription ID:', this.subscriptionId);

    this.homeService.getLabelList(this.subscriptionId!).subscribe({
      next: (response) => {
        this.labelList = response.result || response;
        console.log('✅ Labels loaded successfully:', this.labelList);
        this.loading = false;
      },
      error: (error) => {
        console.error('❌ Error loading labels:', error);
        console.error('❌ Error details:', {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          message: error.message
        });
        this.error = `Failed to load labels: ${error.status} ${error.statusText}. URL: ${error.url}`;
        this.loading = false;
      }
    });
  }

  // Template helper methods
  getCurrentUrl(): string {
    return window.location.href;
  }

  getLocalStorageKeys(): string {
    return Object.keys(localStorage).join(', ') || 'None';
  }
}
