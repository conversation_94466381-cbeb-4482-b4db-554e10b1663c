import { Component, OnInit } from '@angular/core';
import { HomeService } from '../../../services/home.service';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-home',
  imports: [CommonModule],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit {
  subscriptionId: string | null = null;
  batchList: any[] = [];
  stats: any = null;
  labelList: any[] = [];
  loading: boolean = false;
  error: string | null = null;

  constructor(private homeService: HomeService) {}

  ngOnInit(): void {
    this.loadSubscriptionId();
    if (this.subscriptionId) {
      this.loadHomeData();
    } else {
      this.error = 'No subscription ID found. Please ensure you came from the dashboard with a valid subscription ID.';
    }
  }

  private loadSubscriptionId(): void {
    // Get subscription ID from localStorage
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    console.log('Subscription ID from localStorage:', this.subscriptionId);
  }

  private loadHomeData(): void {
    if (!this.subscriptionId) {
      this.error = 'Subscription ID is required for API calls';
      return;
    }

    this.loading = true;
    this.error = null;

    // Load batch list
    this.loadBatchList();

    // Load stats
    this.loadStats();

    // Load labels
    this.loadLabels();
  }

  private loadBatchList(): void {
    console.log('🔍 Loading batch list with subscription ID:', this.subscriptionId);

    this.homeService.getBatchList(
      '1',      // page
      '10',     // size
      '',       // status
      '',       // search
      '',       // start_date
      '',       // end_date
      this.subscriptionId!,
      []        // tags
    ).subscribe({
      next: (response) => {
        this.batchList = response.result || response;
        console.log('✅ Batch list loaded successfully:', this.batchList);
      },
      error: (error) => {
        console.error('❌ Error loading batch list:', error);
        console.error('❌ Error details:', {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          message: error.message
        });
        this.error = `Failed to load batch list: ${error.status} ${error.statusText}. URL: ${error.url}`;
      }
    });
  }

  private loadStats(): void {
    console.log('🔍 Loading stats with subscription ID:', this.subscriptionId);

    this.homeService.getStats(this.subscriptionId!).subscribe({
      next: (response) => {
        this.stats = response.result || response;
        console.log('✅ Stats loaded successfully:', this.stats);
      },
      error: (error) => {
        console.error('❌ Error loading stats:', error);
        console.error('❌ Error details:', {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          message: error.message
        });
        this.error = `Failed to load stats: ${error.status} ${error.statusText}. URL: ${error.url}`;
      }
    });
  }

  private loadLabels(): void {
    console.log('🔍 Loading labels with subscription ID:', this.subscriptionId);

    this.homeService.getLabelList(this.subscriptionId!).subscribe({
      next: (response) => {
        this.labelList = response.result || response;
        console.log('✅ Labels loaded successfully:', this.labelList);
        this.loading = false;
      },
      error: (error) => {
        console.error('❌ Error loading labels:', error);
        console.error('❌ Error details:', {
          status: error.status,
          statusText: error.statusText,
          url: error.url,
          message: error.message
        });
        this.error = `Failed to load labels: ${error.status} ${error.statusText}. URL: ${error.url}`;
        this.loading = false;
      }
    });
  }
}
