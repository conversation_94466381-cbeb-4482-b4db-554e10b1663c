<div class="home-container">
  <h1>Content Enrichment Dashboard</h1>

  <!-- Debug Information -->
  <div class="debug-info" style="background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px;">
    <h3>Debug Information</h3>
    <p><strong>Subscription ID:</strong> {{ subscriptionId || 'Not found' }}</p>
    <p><strong>Loading:</strong> {{ loading }}</p>
    <p><strong>Error:</strong> {{ error || 'None' }}</p>
  </div>

  <!-- Error Display -->
  <div *ngIf="error" class="error-message" style="background: #ffebee; color: #c62828; padding: 10px; margin: 10px 0; border-radius: 4px;">
    <h3>Error</h3>
    <p>{{ error }}</p>
    <p><em>Check the browser console for more details.</em></p>
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="loading" class="loading" style="text-align: center; padding: 20px;">
    <p>Loading data...</p>
  </div>

  <!-- Data Display -->
  <div *ngIf="!loading && !error">
    <!-- Stats Section -->
    <div class="stats-section" style="margin: 20px 0;">
      <h2>Statistics</h2>
      <div *ngIf="stats; else noStats">
        <pre>{{ stats | json }}</pre>
      </div>
      <ng-template #noStats>
        <p>No statistics available</p>
      </ng-template>
    </div>

    <!-- Batch List Section -->
    <div class="batch-section" style="margin: 20px 0;">
      <h2>Batches</h2>
      <div *ngIf="batchList && batchList.length > 0; else noBatches">
        <div *ngFor="let batch of batchList" style="border: 1px solid #ddd; padding: 10px; margin: 5px 0; border-radius: 4px;">
          <pre>{{ batch | json }}</pre>
        </div>
      </div>
      <ng-template #noBatches>
        <p>No batches available</p>
      </ng-template>
    </div>

    <!-- Labels Section -->
    <div class="labels-section" style="margin: 20px 0;">
      <h2>Labels</h2>
      <div *ngIf="labelList && labelList.length > 0; else noLabels">
        <div *ngFor="let label of labelList" style="display: inline-block; background: #e3f2fd; padding: 5px 10px; margin: 2px; border-radius: 4px;">
          {{ label.name || label }}
        </div>
      </div>
      <ng-template #noLabels>
        <p>No labels available</p>
      </ng-template>
    </div>
  </div>
</div>
