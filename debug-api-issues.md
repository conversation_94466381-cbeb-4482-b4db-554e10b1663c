# API 404 Error Debugging Guide

## Current Issues Analysis

Based on your codebase analysis, here are the most likely causes of 404 API errors:

### 1. **Missing Subscription ID**
- **Problem**: APIs require subscription ID in URL path like `/api/subscriptions/{id}/batches/list`
- **Symptom**: URLs become `/api/subscriptions/batches/list` (404 error)
- **Root Cause**: `localStorage.getItem('SubscriptionID')` returns null/undefined

### 2. **Proxy Configuration Issues**
- **Current Proxy**: `proxy.conf.json` routes `/api/*` to `https://m2.app-dev.datax.ai`
- **Dev Server**: Runs on `https://m2.app-local.datax.ai:3000`
- **Potential Issue**: SSL/CORS or target server unreachable

### 3. **Authentication Issues**
- **Auth0 Token**: Required for API calls
- **Interceptor**: Adds Bearer token to `/api/*` requests
- **Potential Issue**: Token expired or invalid

## Debugging Steps

### Step 1: Run the Application
```bash
npm start
# This runs: ng serve --host m2.app-local.datax.ai --port 3000 --ssl true --proxy-config proxy.conf.json
```

### Step 2: Navigate to Home Page
1. Open browser to `https://m2.app-local.datax.ai:3000`
2. Navigate to home page
3. Open browser console (F12)

### Step 3: Check Debug Output
Look for these console messages:

#### ✅ **Good Signs:**
```
🔧 Environment Debug Information:
🔑 Subscription ID: some-valid-id
📍 Full batch list URL: /api/subscriptions/some-valid-id/batches/list
✅ Proxy test successful: [response]
```

#### ❌ **Problem Signs:**
```
❌ No subscription ID found in localStorage
📍 Full batch list URL: /api/subscriptions/batches/list
❌ Proxy test failed: 404/Network error
```

### Step 4: Check Network Tab
1. Open Network tab in browser dev tools
2. Look for API calls to `/api/subscriptions/...`
3. Check:
   - **Request URL**: Should include subscription ID
   - **Status Code**: 404 = wrong URL, 401/403 = auth issue, 0 = network issue
   - **Response Headers**: Check if proxy is working

## Common Solutions

### Solution 1: Fix Missing Subscription ID
If subscription ID is missing:

1. **Check URL Parameters**: Navigate with `?sub=your-subscription-id`
2. **Manual Set**: In browser console:
   ```javascript
   localStorage.setItem('SubscriptionID', 'your-actual-subscription-id');
   location.reload();
   ```

### Solution 2: Fix Proxy Issues
If proxy is not working:

1. **Check Target Server**: Verify `https://m2.app-dev.datax.ai` is accessible
2. **Update Proxy Config**: Modify `proxy.conf.json`:
   ```json
   {
     "/api/*": {
       "target": "https://correct-api-server.com",
       "secure": true,
       "changeOrigin": true,
       "logLevel": "debug"
     }
   }
   ```

### Solution 3: Fix Authentication
If auth issues:

1. **Check Auth0 Config**: Verify environment settings
2. **Clear Storage**: `localStorage.clear()` and re-authenticate
3. **Check Token**: In console: `localStorage.getItem('user')`

## Expected API URLs

With subscription ID `12345`, these URLs should be called:

- **Batch List**: `/api/subscriptions/12345/batches/list`
- **Stats**: `/api/subscriptions/12345/stats`  
- **Labels**: `/api/subscriptions/12345/label_list`

## Next Steps

1. Run the application using `npm start`
2. Check the enhanced debug information on the home page
3. Review console logs for detailed error information
4. Share the console output to identify the specific issue

The enhanced debugging will show exactly what's happening with:
- Subscription ID retrieval
- URL construction
- Proxy configuration
- API call failures

This will help pinpoint whether the issue is:
- Missing subscription ID
- Incorrect API URLs
- Proxy configuration problems
- Authentication failures
- Network connectivity issues
