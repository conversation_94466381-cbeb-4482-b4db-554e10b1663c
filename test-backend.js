const https = require('https');
const dns = require('dns');

// List of potential backend servers to test
const backendServers = [
  'm2.app-dev.datax.ai',
  'app-dev.datax.ai',
  'r2e.app-dev.datax.ai',
  'r3b.app-dev.datax.ai'
];

console.log('🔧 Testing Backend Server Connectivity...\n');

// Test DNS resolution first
async function testDNS(hostname) {
  return new Promise((resolve) => {
    dns.lookup(hostname, (err, address) => {
      if (err) {
        console.log(`❌ DNS FAILED: ${hostname} - ${err.message}`);
        resolve(false);
      } else {
        console.log(`✅ DNS SUCCESS: ${hostname} -> ${address}`);
        resolve(true);
      }
    });
  });
}

// Test HTTPS connection
async function testHTTPS(hostname) {
  return new Promise((resolve) => {
    const options = {
      hostname: hostname,
      port: 443,
      path: '/api/subscriptions/D1PEKO7/stats',
      method: 'GET',
      timeout: 5000
    };

    const req = https.request(options, (res) => {
      console.log(`✅ HTTPS SUCCESS: ${hostname} - Status: ${res.statusCode}`);
      resolve(true);
    });

    req.on('error', (err) => {
      console.log(`❌ HTTPS FAILED: ${hostname} - ${err.message}`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log(`❌ HTTPS TIMEOUT: ${hostname}`);
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// Main test function
async function runTests() {
  for (const server of backendServers) {
    console.log(`\n🔍 Testing: ${server}`);
    console.log('─'.repeat(50));
    
    const dnsOk = await testDNS(server);
    if (dnsOk) {
      await testHTTPS(server);
    }
  }
  
  console.log('\n🎯 Summary:');
  console.log('- Use servers that show "DNS SUCCESS" and "HTTPS SUCCESS" in your proxy');
  console.log('- Status codes 401/403 are OK (authentication required)');
  console.log('- Status codes 404 might be OK (endpoint might not exist)');
  console.log('- DNS or connection failures mean the server is not accessible');
}

runTests().catch(console.error);
